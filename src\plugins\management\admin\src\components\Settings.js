import React, { useState, useEffect } from 'react';
import { Switch, Route, useHistory, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Typography,
  Button,
  Form,
  Input,
  Switch as AntSwitch,
  Select,
  Upload,
  message,
  Spin,
} from 'antd';
import { useFetchClient } from '@strapi/helper-plugin';
import {
  SettingOutlined,
  BuildOutlined,
  CreditCardOutlined,
  UploadOutlined,
  SaveOutlined,
  CarOutlined,
  WechatOutlined,
  CalculatorOutlined,
  PercentageOutlined,
  MobileOutlined,
  HomeOutlined,
  ShoppingOutlined,
  TagsOutlined,
  AppstoreOutlined,
  UserOutlined,
  HeartOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';

// Import shared components
import {
  PageContainer,
  Card,
  CardContent,
} from '../components/shared/StyledComponents';
import PageHeader from '../components/shared/PageHeader';
import SharedImageUpload from '../components/shared/SharedImageUpload';

const { Sider, Content } = Layout;
const { Title } = Typography;
const { Option } = Select;

const SettingsContainer = styled.div`
  font-family: 'Be Vietnam Pro', sans-serif;
  min-height: 100vh;
  background: #f8fafc;

  .settings-layout {
    background: #f8fafc;
    min-height: 100vh;
  }

  .settings-sider {
    background: #ffffff;
    border-radius: 8px;
    height: calc(100vh - 48px);
    overflow: hidden;
    border: 1px solid #e5e7eb;
  }

  .settings-menu {
    border-right: none;
    background: transparent;
    padding: 16px 0;
  }

  .settings-menu .ant-menu-item {
    margin: 0;
    border-radius: 0;
    width: 100%;
    height: 48px;
    line-height: 48px;
    display: flex;
    align-items: center;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .settings-menu .ant-menu-item-selected {
    background: #2563eb;
    color: white;
  }

  .settings-menu .ant-menu-item:hover {
    background: #f9fafb;
    color: #2563eb;
  }

  .settings-menu .ant-menu-item-selected:hover {
    background: #1d4ed8;
    color: white;
  }

  .settings-content {
    background: #f8fafc;
  }

  .settings-form .ant-form-item-label > label {
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 500;
    color: #374151;
  }

  .settings-form .ant-form-item {
    margin-bottom: 20px;
  }

  .settings-form .ant-input,
  .settings-form .ant-select-selector,
  .settings-form .ant-input-password {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    font-family: 'Be Vietnam Pro', sans-serif;
    transition: border-color 0.2s ease;
    padding: 8px 12px;
    font-size: 14px;
  }

  .settings-form .ant-input:focus,
  .settings-form .ant-select-focused .ant-select-selector,
  .settings-form .ant-input-password:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .settings-form .ant-switch {
    background-color: #d1d5db;
  }

  .settings-form .ant-switch-checked {
    background-color: #2563eb;
  }

  .settings-form .ant-upload.ant-upload-select-picture-card {
    border: 1px dashed #d1d5db;
    border-radius: 6px;
    background-color: #fafafa;
    transition: border-color 0.2s ease;
  }

  .settings-form .ant-upload.ant-upload-select-picture-card:hover {
    border-color: #2563eb;
  }

  .settings-save-btn {
    background: #2563eb;
    border: none;
    border-radius: 6px;
    height: 40px;
    font-family: 'Be Vietnam Pro', sans-serif;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .settings-save-btn:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  }

  .ant-upload.ant-upload-select-picture-card:hover .upload-overlay {
    opacity: 1 !important;
  }
`;

// Component cho Cài đặt chung
const GeneralSettings = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('shipping');
  const [enableFreeShipping, setEnableFreeShipping] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { get, put } = useFetchClient();

  // Load all settings on component mount
  React.useEffect(() => {
    loadAllSettings();
  }, []);

  const loadAllSettings = async () => {
    try {
      setInitialLoading(true);
      // Load shipping settings
      await loadShippingSettings();
      console.log('✅ All settings loaded');
    } catch (error) {
      console.error('❌ Error loading settings:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  const loadShippingSettings = async () => {
    try {
      console.log('🔄 Loading shipping settings...');
      const response = await get('/management/settings/shipping');

      if (
        response &&
        response.data &&
        response.data.success &&
        response.data.data
      ) {
        const data = response.data.data;
        console.log('📄 Loaded shipping settings:', data);

        // Update form values
        form.setFieldsValue({
          enableFreeShipping: data.enableFreeShipping,
          freeShippingThreshold: data.freeShippingThreshold,
          shippingFee: data.shippingFee,
        });

        // Update state for conditional rendering
        setEnableFreeShipping(data.enableFreeShipping);
      }
    } catch (error) {
      console.error('❌ Error loading shipping settings:', error);
      message.error('Không thể tải cài đặt vận chuyển');
      throw error; // Re-throw to handle in loadAllSettings
    }
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      console.log('💾 Saving shipping settings:', values);

      const response = await put('/management/settings/shipping', values);

      if (response && response.data && response.data.success) {
        message.success('Cài đặt vận chuyển đã được lưu thành công!');
        console.log('✅ Saved shipping settings:', response.data.data);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving shipping settings:', error);
      message.error('Có lỗi xảy ra khi lưu cài đặt vận chuyển');
    } finally {
      setLoading(false);
    }
  };

  const tabItems = [
    {
      key: 'shipping',
      label: 'Phí vận chuyển',
      icon: <CarOutlined />,
    },
    {
      key: 'oa',
      label: 'Cài đặt Zalo Mini App',
      icon: <WechatOutlined />,
    },
    {
      key: 'tax',
      label: 'Cài đặt thuế',
      icon: <CalculatorOutlined />,
    },
    {
      key: 'commission',
      label: 'Chính sách hoa hồng',
      icon: <PercentageOutlined />,
    },
    {
      key: 'ranks',
      label: 'Cấp bậc thành viên',
      icon: <TagsOutlined />,
    },
  ];

  const renderShippingContent = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      className="settings-form"
      initialValues={{
        enableFreeShipping: false,
        freeShippingThreshold: 0,
        shippingFee: 0,
      }}
    >
      <Form.Item
        label="Bật miễn phí vận chuyển"
        name="enableFreeShipping"
        valuePropName="checked"
      >
        <AntSwitch onChange={setEnableFreeShipping} disabled={loading} />
      </Form.Item>

      {enableFreeShipping && (
        <Form.Item
          label="Ngưỡng miễn phí vận chuyển (VNĐ)"
          name="freeShippingThreshold"
        >
          <Input
            type="number"
            placeholder="Nhập ngưỡng miễn phí vận chuyển"
            disabled={loading}
          />
        </Form.Item>
      )}

      <Form.Item label="Phí vận chuyển (VNĐ)" name="shippingFee">
        <Input
          type="number"
          placeholder="Nhập phí vận chuyển"
          disabled={loading}
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          className="settings-save-btn"
          icon={<SaveOutlined />}
          loading={loading}
        >
          Lưu cài đặt
        </Button>
      </Form.Item>
    </Form>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'shipping':
        return renderShippingContent();
      case 'oa':
        return <ZaloMiniAppSettings />;
      case 'tax':
        return <TaxSettings />;
      case 'commission':
        return <CommissionSettings />;
      case 'ranks':
        return <RankSettings />;
      default:
        return renderShippingContent();
    }
  };

  // Show loading spinner while data is being loaded
  if (initialLoading) {
    return (
      <Card>
        <PageHeader
          title="Cài đặt chung"
          description="Cấu hình các thông tin cơ bản của hệ thống"
        />
        <CardContent>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '200px',
            }}
          >
            <Spin size="large" tip="Đang tải cài đặt..." />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <PageHeader
        title="Cài đặt chung"
        description="Cấu hình các thông tin cơ bản của hệ thống"
      />
      <CardContent>
        <div style={{ marginBottom: 24 }}>
          <div
            style={{
              display: 'flex',
              gap: '16px',
              borderBottom: '1px solid #e5e7eb',
              marginBottom: 24,
            }}
          >
            {tabItems.map((tab) => (
              <div
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                style={{
                  padding: '12px 16px',
                  cursor: 'pointer',
                  borderBottom:
                    activeTab === tab.key
                      ? '2px solid #2563eb'
                      : '2px solid transparent',
                  color: activeTab === tab.key ? '#2563eb' : '#6b7280',
                  fontFamily: 'Be Vietnam Pro',
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s ease',
                }}
              >
                {tab.icon}
                {tab.label}
              </div>
            ))}
          </div>
        </div>
        {renderContent()}
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt Zalo Mini App
const ZaloMiniAppSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { get, put } = useFetchClient();

  // Load Zalo Mini App settings on component mount
  React.useEffect(() => {
    loadZaloMiniAppSettings();
  }, []);

  const loadZaloMiniAppSettings = async () => {
    try {
      setInitialLoading(true);
      console.log('🔄 Loading Zalo Mini App settings...');
      const response = await get('/management/settings/zalo-mini-app');

      if (
        response &&
        response.data &&
        response.data.success &&
        response.data.data
      ) {
        const data = response.data.data;
        console.log('📄 Loaded Zalo Mini App settings:', data);

        // Update form values
        form.setFieldsValue({
          oaId: data.oaId || '',
          appId: data.appId || '',
          appSecret: data.appSecret || '',
          checkoutPrivateKey: data.checkoutPrivateKey || '',
        });
      }
    } catch (error) {
      console.error('❌ Error loading Zalo Mini App settings:', error);
      message.error('Không thể tải cài đặt Zalo Mini App');
    } finally {
      setInitialLoading(false);
    }
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      console.log('💾 Saving Zalo Mini App settings:', values);

      const response = await put('/management/settings/zalo-mini-app', values);
      console.log('💾 Save response:', response);

      if (response && response.data && response.data.success) {
        message.success('Cài đặt Zalo Mini App đã được lưu thành công!');
        console.log('✅ Saved Zalo Mini App settings:', values);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving Zalo Mini App settings:', error);
      message.error('Có lỗi xảy ra khi lưu cài đặt Zalo Mini App');
    } finally {
      setLoading(false);
    }
  };

  // Show loading spinner while data is being loaded
  if (initialLoading) {
    return (
      <Card>
        <CardContent>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '200px',
            }}
          >
            <Spin size="large" tip="Đang tải cài đặt Zalo Mini App..." />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <div
          style={{
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: 16,
            marginBottom: 24,
          }}
        >
          <Title
            level={5}
            style={{
              marginBottom: 8,
              fontFamily: 'Be Vietnam Pro',
              color: '#374151',
              fontSize: 16,
              fontWeight: 600,
            }}
          >
            Thông tin Zalo Mini App
          </Title>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
          initialValues={{
            oaId: '',
            appId: '',
            appSecret: '',
            checkoutPrivateKey: '',
          }}
        >
          <Form.Item label="OA ID" name="oaId">
            <Input placeholder="Nhập OA ID" disabled={loading} />
          </Form.Item>

          <Form.Item label="App ID" name="appId">
            <Input placeholder="Nhập App ID" disabled={loading} />
          </Form.Item>

          <Form.Item label="App Secret" name="appSecret">
            <Input.Password placeholder="Nhập App Secret" disabled={loading} />
          </Form.Item>

          <Form.Item label="Checkout Private Key" name="checkoutPrivateKey">
            <Input.TextArea
              placeholder="Nhập Checkout Private Key"
              rows={4}
              disabled={loading}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt thuế
const TaxSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { get, put } = useFetchClient();

  // Load tax settings on component mount
  React.useEffect(() => {
    loadTaxSettings();
  }, []);

  const loadTaxSettings = async () => {
    try {
      setInitialLoading(true);
      console.log('🔄 Loading tax settings...');
      const response = await get('/management/settings/tax');

      if (
        response &&
        response.data &&
        response.data.success &&
        response.data.data
      ) {
        const data = response.data.data;
        console.log('📄 Loaded tax settings:', data);

        // Update form values
        form.setFieldsValue({
          vatRate: data.vatRate || '',
        });
      }
    } catch (error) {
      console.error('❌ Error loading tax settings:', error);
      message.error('Không thể tải cài đặt thuế');
    } finally {
      setInitialLoading(false);
    }
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      console.log('💾 Saving tax settings:', values);

      const response = await put('/management/settings/tax', values);
      console.log('💾 Save response:', response);

      if (response && response.data && response.data.success) {
        message.success('Cài đặt thuế đã được lưu thành công!');
        console.log('✅ Saved tax settings:', values);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving tax settings:', error);
      message.error('Có lỗi xảy ra khi lưu cài đặt thuế');
    } finally {
      setLoading(false);
    }
  };

  // Show loading spinner while data is being loaded
  if (initialLoading) {
    return (
      <Card>
        <CardContent>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '200px',
            }}
          >
            <Spin size="large" tip="Đang tải cài đặt thuế..." />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <div
          style={{
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: 16,
            marginBottom: 24,
          }}
        >
          <Title
            level={5}
            style={{
              marginBottom: 8,
              fontFamily: 'Be Vietnam Pro',
              color: '#374151',
              fontSize: 16,
              fontWeight: 600,
            }}
          >
            Cài đặt thuế
          </Title>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
          initialValues={{
            vatRate: '',
          }}
        >
          <Form.Item label="Tỷ lệ thuế VAT (%)" name="vatRate">
            <Input
              type="number"
              placeholder="Nhập tỷ lệ thuế VAT"
              disabled={loading}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt hoa hồng
const CommissionSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [maxLevels, setMaxLevels] = useState(3);
  const { get, put } = useFetchClient();

  // Load commission settings on component mount
  React.useEffect(() => {
    loadCommissionSettings();
  }, []);

  const loadCommissionSettings = async () => {
    try {
      setInitialLoading(true);
      console.log('🔄 Loading commission settings...');
      const response = await get('/management/settings/commission');

      if (
        response &&
        response.data &&
        response.data.success &&
        response.data.data
      ) {
        const data = response.data.data;
        console.log('📄 Loaded commission settings:', data);

        // Update form values and maxLevels state
        setMaxLevels(data.maxLevels || 3);
        form.setFieldsValue({
          enableCommissionSystem: data.enableCommissionSystem ?? false,
          maxLevels: data.maxLevels || '',
          f1CommissionRate: data.f1CommissionRate || '',
          f2CommissionRate: data.f2CommissionRate || '',
          f3CommissionRate: data.f3CommissionRate || '',
          f4CommissionRate: data.f4CommissionRate || '',
          f5CommissionRate: data.f5CommissionRate || '',
        });
      }
    } catch (error) {
      console.error('❌ Error loading commission settings:', error);
      message.error('Không thể tải cài đặt hoa hồng');
    } finally {
      setInitialLoading(false);
    }
  };

  const handleMaxLevelsChange = (value) => {
    setMaxLevels(value);
    // Only clear rates for levels beyond the selected max, don't set defaults
    const updates = {};

    // Clear rates for levels beyond the selected max
    for (let i = value + 1; i <= 5; i++) {
      updates[`f${i}CommissionRate`] = '';
    }

    form.setFieldsValue(updates);
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      console.log('💾 Saving commission settings:', values);

      const response = await put('/management/settings/commission', values);
      console.log('💾 Save response:', response);

      if (response && response.data && response.data.success) {
        message.success('Cài đặt hoa hồng đã được lưu thành công!');
        console.log('✅ Saved commission settings:', values);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving commission settings:', error);
      message.error('Có lỗi xảy ra khi lưu cài đặt hoa hồng');
    } finally {
      setLoading(false);
    }
  };

  // Show loading spinner while data is being loaded
  if (initialLoading) {
    return (
      <Card>
        <CardContent>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '200px',
            }}
          >
            <Spin size="large" tip="Đang tải cài đặt hoa hồng..." />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <div
          style={{
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: 16,
            marginBottom: 24,
          }}
        >
          <Title
            level={5}
            style={{
              marginBottom: 8,
              fontFamily: 'Be Vietnam Pro',
              color: '#374151',
              fontSize: 16,
              fontWeight: 600,
            }}
          >
            Cài đặt hệ thống hoa hồng
          </Title>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
          initialValues={{
            enableCommissionSystem: false,
            maxLevels: '',
            f1CommissionRate: '',
            f2CommissionRate: '',
            f3CommissionRate: '',
          }}
        >
          <Form.Item
            label="Bật hệ thống hoa hồng đa cấp"
            name="enableCommissionSystem"
            valuePropName="checked"
          >
            <AntSwitch disabled={loading} />
          </Form.Item>

          <Form.Item label="Số cấp tối đa" name="maxLevels">
            <Select onChange={handleMaxLevelsChange} disabled={loading}>
              <Option value={2}>2 cấp (F0 → F1)</Option>
              <Option value={3}>3 cấp (F0 → F1 → F2)</Option>
              <Option value={4}>4 cấp (F0 → F1 → F2 → F3)</Option>
              <Option value={5}>5 cấp (F0 → F1 → F2 → F3 → F4)</Option>
            </Select>
          </Form.Item>

          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
              marginTop: 32,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Tỷ lệ hoa hồng theo cấp
            </Title>
            <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
              F0 mua hàng →{' '}
              {Array.from(
                { length: maxLevels - 1 },
                (_, i) => `F${i + 1}`
              ).join(', ')}{' '}
              nhận hoa hồng theo % đơn hàng
            </p>
          </div>

          {/* F1 Commission - Always show for levels >= 2 */}
          {maxLevels >= 2 && (
            <Form.Item
              label="Hoa hồng F1 (%)"
              name="f1CommissionRate"
              rules={[
                { required: true, message: 'Vui lòng nhập tỷ lệ hoa hồng F1!' },
                {
                  type: 'number',
                  min: 0,
                  max: 100,
                  message: 'Tỷ lệ phải từ 0-100%!',
                },
              ]}
            >
              <Input
                type="number"
                placeholder="Nhập tỷ lệ hoa hồng F1"
                suffix="%"
                min={0}
                max={100}
                step={0.1}
                disabled={loading}
              />
            </Form.Item>
          )}

          {/* F2 Commission - Show for levels >= 3 */}
          {maxLevels >= 3 && (
            <Form.Item
              label="Hoa hồng F2 (%)"
              name="f2CommissionRate"
              rules={[
                { required: true, message: 'Vui lòng nhập tỷ lệ hoa hồng F2!' },
                {
                  type: 'number',
                  min: 0,
                  max: 100,
                  message: 'Tỷ lệ phải từ 0-100%!',
                },
              ]}
            >
              <Input
                type="number"
                placeholder="Nhập tỷ lệ hoa hồng F2"
                suffix="%"
                min={0}
                max={100}
                step={0.1}
                disabled={loading}
              />
            </Form.Item>
          )}

          {/* F3 Commission - Show for levels >= 4 */}
          {maxLevels >= 4 && (
            <Form.Item
              label="Hoa hồng F3 (%)"
              name="f3CommissionRate"
              rules={[
                {
                  type: 'number',
                  min: 0,
                  max: 100,
                  message: 'Tỷ lệ phải từ 0-100%!',
                },
              ]}
            >
              <Input
                type="number"
                placeholder="Nhập tỷ lệ hoa hồng F3"
                suffix="%"
                min={0}
                max={100}
                step={0.1}
                disabled={loading}
              />
            </Form.Item>
          )}

          {/* F4 Commission - Show for levels >= 5 */}
          {maxLevels >= 5 && (
            <Form.Item
              label="Hoa hồng F4 (%)"
              name="f4CommissionRate"
              rules={[
                {
                  type: 'number',
                  min: 0,
                  max: 100,
                  message: 'Tỷ lệ phải từ 0-100%!',
                },
              ]}
            >
              <Input
                type="number"
                placeholder="Nhập tỷ lệ hoa hồng F4"
                suffix="%"
                min={0}
                max={100}
                step={0.1}
                disabled={loading}
              />
            </Form.Item>
          )}

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt cấp bậc thành viên
const RankSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [ranks, setRanks] = useState([]);
  const [products, setProducts] = useState([]);
  const { get, put } = useFetchClient();

  // Load rank settings on component mount
  React.useEffect(() => {
    loadRankSettings();
  }, []);

  const loadRankSettings = async () => {
    try {
      setInitialLoading(true);
      console.log('🔄 Loading rank settings...');

      // Load both ranks and products
      const [ranksResponse, productsResponse] = await Promise.all([
        get('/management/settings/ranks'),
        get('/management/products'),
      ]);

      // Handle ranks data
      if (
        ranksResponse &&
        ranksResponse.data &&
        ranksResponse.data.success &&
        ranksResponse.data.data
      ) {
        const ranksData = ranksResponse.data.data;
        console.log('📄 Loaded ranks:', ranksData);
        setRanks(ranksData.ranks || []);
      }

      // Handle products data
      if (
        productsResponse &&
        productsResponse.data &&
        productsResponse.data.success
      ) {
        console.log('📄 Loaded products response:', productsResponse);
        const productsData = productsResponse.data.data || [];
        console.log('📄 Final products data:', productsData);
        setProducts(productsData);
      } else {
        console.log('❌ No products response or data');
      }
    } catch (error) {
      console.error('❌ Error loading rank settings:', error);
      message.error('Không thể tải cài đặt cấp bậc');
    } finally {
      setInitialLoading(false);
    }
  };

  const addRank = () => {
    const newRanks = [
      ...ranks,
      {
        id: Date.now(),
        name: '',
        description: '',
        productIds: [],
      },
    ];
    setRanks(newRanks);
  };

  const removeRank = (index) => {
    const newRanks = ranks.filter((_, i) => i !== index);
    setRanks(newRanks);
  };

  const updateRank = (index, field, value) => {
    const newRanks = [...ranks];
    newRanks[index] = { ...newRanks[index], [field]: value };
    setRanks(newRanks);
  };

  const onFinish = async () => {
    try {
      setLoading(true);
      console.log('💾 Saving rank settings:', ranks);

      const response = await put('/management/settings/ranks', { ranks });
      console.log('💾 Save response:', response);

      if (response && response.data && response.data.success) {
        message.success('Cài đặt cấp bậc đã được lưu thành công!');
        console.log('✅ Saved rank settings:', ranks);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving rank settings:', error);
      message.error('Có lỗi xảy ra khi lưu cài đặt cấp bậc');
    } finally {
      setLoading(false);
    }
  };

  // Show loading spinner while data is being loaded
  if (initialLoading) {
    return (
      <Card>
        <CardContent>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '200px',
            }}
          >
            <Spin size="large" tip="Đang tải cài đặt cấp bậc..." />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <div
          style={{
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: 16,
            marginBottom: 24,
          }}
        >
          <Title
            level={5}
            style={{
              marginBottom: 8,
              fontFamily: 'Be Vietnam Pro',
              color: '#374151',
              fontSize: 16,
              fontWeight: 600,
            }}
          >
            Cài đặt cấp bậc thành viên
          </Title>
          <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
            Thiết lập các cấp bậc và sản phẩm để thành viên có thể nâng cấp
          </p>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
        >
          {ranks.map((rank, index) => (
            <div
              key={rank.id || index}
              style={{
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px',
                backgroundColor: '#fafafa',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '16px',
                }}
              >
                <Title
                  level={5}
                  style={{ margin: 0, fontFamily: 'Be Vietnam Pro' }}
                >
                  Cấp bậc #{index + 1}
                </Title>
                <Button
                  type="text"
                  danger
                  onClick={() => removeRank(index)}
                  disabled={loading}
                >
                  Xóa
                </Button>
              </div>

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '16px',
                  marginBottom: '16px',
                }}
              >
                <div>
                  <label
                    style={{
                      display: 'block',
                      marginBottom: '8px',
                      fontWeight: 500,
                    }}
                  >
                    Tên cấp bậc
                  </label>
                  <Input
                    placeholder="VD: Thành viên mới, Đại lý, Giám đốc kinh doanh..."
                    value={rank.name}
                    onChange={(e) => updateRank(index, 'name', e.target.value)}
                    disabled={loading}
                  />
                </div>
                <div>
                  <label
                    style={{
                      display: 'block',
                      marginBottom: '8px',
                      fontWeight: 500,
                    }}
                  >
                    Mô tả
                  </label>
                  <Input
                    placeholder="Mô tả về cấp bậc này"
                    value={rank.description}
                    onChange={(e) =>
                      updateRank(index, 'description', e.target.value)
                    }
                    disabled={loading}
                  />
                </div>
              </div>

              <div>
                <label
                  style={{
                    display: 'block',
                    marginBottom: '8px',
                    fontWeight: 500,
                  }}
                >
                  Sản phẩm để nâng cấp
                </label>
                <Select
                  mode="multiple"
                  placeholder="Chọn sản phẩm mà khi mua sẽ được nâng lên cấp bậc này"
                  value={rank.productIds}
                  onChange={(value) => updateRank(index, 'productIds', value)}
                  disabled={loading}
                  style={{ width: '100%' }}
                  showSearch
                  filterOption={(input, option) => {
                    const label = option?.label || option?.children;
                    return typeof label === 'string'
                      ? label.toLowerCase().includes(input.toLowerCase())
                      : false;
                  }}
                >
                  {products.map((product) => (
                    <Select.Option key={product.id} value={product.id}>
                      {product.name || `Sản phẩm #${product.id}`}
                    </Select.Option>
                  ))}
                </Select>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '12px',
                    margin: '4px 0 0 0',
                  }}
                >
                  Thành viên sẽ được nâng lên cấp bậc này khi mua bất kỳ sản
                  phẩm nào trong danh sách
                </p>
              </div>
            </div>
          ))}

          <div style={{ marginBottom: '24px' }}>
            <Button
              type="dashed"
              onClick={addRank}
              disabled={loading}
              style={{ width: '100%', height: '48px' }}
            >
              + Thêm cấp bậc mới
            </Button>
          </div>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              Lưu cài đặt
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Phương thức thanh toán
const PaymentSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [enableBankTransfer, setEnableBankTransfer] = useState(false);
  const [enableVNPay, setEnableVNPay] = useState(false);

  // Use Strapi's fetch client with authentication
  const { get, put } = useFetchClient();

  // Load payment settings on component mount
  useEffect(() => {
    loadPaymentSettings();
  }, []);

  const loadPaymentSettings = async () => {
    try {
      setInitialLoading(true);
      console.log('🔍 Loading payment settings...');

      const response = await get('/management/settings/payment');

      if (response && response.data && response.data.success) {
        const paymentData = response.data.data;
        console.log('📄 Loaded payment settings:', paymentData);
        form.setFieldsValue(paymentData);

        // Update toggle states
        setEnableBankTransfer(paymentData.enableBankTransfer || false);
        setEnableVNPay(paymentData.enableVNPay || false);
      }
    } catch (error) {
      console.error('❌ Error loading payment settings:', error);
      message.error('Không thể tải cài đặt thanh toán');
    } finally {
      setInitialLoading(false);
    }
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      console.log('💾 Saving payment settings:', values);

      const response = await put('/management/settings/payment', values);

      if (response && response.data && response.data.success) {
        console.log('✅ Payment settings saved successfully');
        message.success(
          'Cài đặt phương thức thanh toán đã được lưu thành công!'
        );
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving payment settings:', error);
      message.error('Không thể lưu cài đặt thanh toán');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Card>
        <PageHeader
          title="Phương thức thanh toán"
          description="Cấu hình các phương thức thanh toán cho hệ thống"
        />
        <CardContent>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16, color: '#666' }}>
              Đang tải cài đặt thanh toán...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <PageHeader
        title="Phương thức thanh toán"
        description="Cấu hình các phương thức thanh toán cho hệ thống"
      />
      <CardContent>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
        >
          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Phương thức thanh toán khi nhận hàng
            </Title>
          </div>

          <Form.Item
            label="Thanh toán khi nhận hàng (COD)"
            name="enableCOD"
            valuePropName="checked"
          >
            <AntSwitch />
          </Form.Item>

          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
              marginTop: 32,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Chuyển khoản ngân hàng
            </Title>
          </div>

          <Form.Item
            label="Bật chuyển khoản ngân hàng"
            name="enableBankTransfer"
            valuePropName="checked"
          >
            <AntSwitch onChange={(checked) => setEnableBankTransfer(checked)} />
          </Form.Item>

          <Form.Item label="Tên ngân hàng" name="bankName">
            <Input
              placeholder="Nhập tên ngân hàng"
              disabled={!enableBankTransfer}
            />
          </Form.Item>

          <Form.Item label="Số tài khoản" name="bankAccount">
            <Input
              placeholder="Nhập số tài khoản"
              disabled={!enableBankTransfer}
            />
          </Form.Item>

          <Form.Item label="Chủ tài khoản" name="bankOwner">
            <Input
              placeholder="Nhập tên chủ tài khoản"
              disabled={!enableBankTransfer}
            />
          </Form.Item>

          <div
            style={{
              borderBottom: '1px solid #e5e7eb',
              paddingBottom: 16,
              marginBottom: 24,
              marginTop: 32,
            }}
          >
            <Title
              level={5}
              style={{
                marginBottom: 8,
                fontFamily: 'Be Vietnam Pro',
                color: '#374151',
                fontSize: 16,
                fontWeight: 600,
              }}
            >
              Ví điện tử
            </Title>
          </div>

          <Form.Item
            label="Bật VNPay"
            name="enableVNPay"
            valuePropName="checked"
          >
            <AntSwitch onChange={(checked) => setEnableVNPay(checked)} />
          </Form.Item>

          <Form.Item label="VNPay Merchant ID" name="vnpayMerchantId">
            <Input
              placeholder="Nhập VNPay Merchant ID"
              disabled={!enableVNPay}
            />
          </Form.Item>

          <Form.Item label="VNPay Secret Key" name="vnpaySecretKey">
            <Input.Password
              placeholder="Nhập VNPay Secret Key"
              disabled={!enableVNPay}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="settings-save-btn"
              icon={<SaveOutlined />}
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Đang lưu...' : 'Lưu cài đặt'}
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt công ty
const CompanySettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Use Strapi's fetch client with authentication
  const { get, put } = useFetchClient();

  // Load company settings on component mount
  useEffect(() => {
    loadCompanySettings();
  }, []);

  const loadCompanySettings = async () => {
    try {
      setInitialLoading(true);
      console.log('🔍 Loading company settings...');

      const response = await get('/management/settings/company');

      if (response && response.data && response.data.success) {
        const companyData = response.data.data;
        console.log('📄 Loaded company settings:', companyData);
        form.setFieldsValue(companyData);
      }
    } catch (error) {
      console.error('❌ Error loading company settings:', error);
      message.error('Không thể tải cài đặt công ty');
    } finally {
      setInitialLoading(false);
    }
  };

  const onFinish = async (values) => {
    try {
      setLoading(true);
      console.log('💾 Saving company settings:', values);

      const response = await put('/management/settings/company', values);

      if (response && response.data && response.data.success) {
        console.log('✅ Company settings saved successfully');
        message.success('Cài đặt công ty đã được lưu thành công!');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error saving company settings:', error);
      message.error('Không thể lưu cài đặt công ty');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Card>
        <PageHeader
          title="Cài đặt công ty"
          description="Thông tin chi tiết về công ty và tổ chức"
        />
        <CardContent>
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16, color: '#666' }}>
              Đang tải cài đặt công ty...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <PageHeader
        title="Cài đặt công ty"
        description="Thông tin chi tiết về công ty và tổ chức"
      />
      <CardContent>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          className="settings-form"
        >
          <Form.Item
            label="Tên công ty"
            name="companyName"
            rules={[{ required: true, message: 'Vui lòng nhập tên công ty!' }]}
          >
            <Input placeholder="Nhập tên công ty" />
          </Form.Item>

          <Form.Item
            label="Địa chỉ công ty"
            name="companyAddress"
            rules={[
              { required: true, message: 'Vui lòng nhập địa chỉ công ty!' },
            ]}
          >
            <Input.TextArea rows={2} placeholder="Nhập địa chỉ công ty" />
          </Form.Item>

          <Form.Item
            label="Số điện thoại"
            name="companyPhone"
            rules={[
              { required: true, message: 'Vui lòng nhập số điện thoại!' },
            ]}
          >
            <Input placeholder="Nhập số điện thoại" />
          </Form.Item>

          <Form.Item
            label="Email"
            name="companyEmail"
            rules={[
              { required: true, message: 'Vui lòng nhập email!' },
              { type: 'email', message: 'Email không hợp lệ!' },
            ]}
          >
            <Input placeholder="Nhập email công ty" />
          </Form.Item>

          <Form.Item label="Mã số thuế" name="taxCode">
            <Input placeholder="Nhập mã số thuế" />
          </Form.Item>

          <Form.Item label="Website" name="website">
            <Input placeholder="Nhập website công ty" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="settings-save-btn"
              icon={<SaveOutlined />}
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Đang lưu...' : 'Lưu cài đặt'}
            </Button>
          </Form.Item>
        </Form>
      </CardContent>
    </Card>
  );
};

// Component cho Cài đặt giao diện Mini App
const MiniAppSettings = () => {
  const [form] = Form.useForm();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [appSettings, setAppSettings] = useState({
    logo: null,
    title: 'LibertyHolding xin chào!',
    subtitle: 'Ngày mới tốt lành👋',
    primaryColor: '#2563eb',
    colorType: 'solid',
    gradientStart: '#667eea',
    gradientEnd: '#764ba2',
  });

  // Logo upload state
  const [logoFileList, setLogoFileList] = useState([]);

  // Use Strapi's fetch client with authentication
  const { get, put, post } = useFetchClient();

  // Banner slides data
  const bannerSlides = [
    {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      text: 'Khuyến mãi đặc biệt',
      subtext: 'Giảm giá lên đến 50%',
    },
    {
      background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      text: 'Sản phẩm mới',
      subtext: 'Bộ sưu tập 2024',
    },
    {
      background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      text: 'Miễn phí vận chuyển',
      subtext: 'Đơn hàng từ 500k',
    },
  ];

  // Auto slide effect
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % bannerSlides.length);
    }, 3000); // Change slide every 3 seconds

    return () => clearInterval(interval);
  }, [bannerSlides.length]);

  // Load current settings
  React.useEffect(() => {
    const loadSettings = async () => {
      try {
        console.log('Loading settings...');
        const response = await get('/management/settings');
        console.log('Settings response:', response);

        // Check if response has data property and it's successful
        if (
          response &&
          response.data &&
          response.data.success &&
          response.data.data
        ) {
          const data = response.data.data;
          console.log('Settings data:', data);

          // Update app settings state (without logo first)
          setAppSettings({
            logo: null, // Will be set by useEffect when logoFileList changes
            title: data.title || 'LibertyHolding xin chào!',
            subtitle: data.subtitle || 'Ngày mới tốt lành👋',
            primaryColor: data.primaryColor || '#2563eb',
            colorType: data.colorType || 'solid',
            gradientStart: data.gradientStart || '#667eea',
            gradientEnd: data.gradientEnd || '#764ba2',
          });

          // Update logo file list for SharedImageUpload (this will trigger useEffect to update logo)
          if (data.logo?.url) {
            setLogoFileList([
              {
                uid: data.logo.id || 'logo',
                name: data.logo.name || 'logo.png',
                status: 'done',
                url: data.logo.url,
              },
            ]);
          } else {
            setLogoFileList([]);
          }

          // Update form values
          form.setFieldsValue({
            title: data.title,
            subtitle: data.subtitle,
            colorType: data.colorType,
            primaryColor: data.primaryColor,
            gradientStart: data.gradientStart,
            gradientEnd: data.gradientEnd,
          });

          console.log('✅ Settings loaded and applied');
        } else {
          console.log('⚠️ No settings data found in response');
        }
      } catch (error) {
        console.error('❌ Error loading settings:', error);
      }
    };

    loadSettings();
  }, [form, get]);

  // Update appSettings.logo when logoFileList changes
  React.useEffect(() => {
    if (logoFileList.length > 0) {
      const logoFile = logoFileList[0];
      if (logoFile.url) {
        setAppSettings((prev) => ({ ...prev, logo: logoFile.url }));
      } else if (logoFile.originFileObj) {
        // For new uploaded files, create preview URL
        const previewUrl = URL.createObjectURL(logoFile.originFileObj);
        setAppSettings((prev) => ({ ...prev, logo: previewUrl }));
      }
    } else {
      setAppSettings((prev) => ({ ...prev, logo: null }));
    }
  }, [logoFileList]);

  // Function to reload settings
  const reloadSettings = async () => {
    try {
      console.log('🔄 Reloading settings...');
      const response = await get('/management/settings');

      if (
        response &&
        response.data &&
        response.data.success &&
        response.data.data
      ) {
        const data = response.data.data;
        console.log('🔄 Reloaded settings data:', data);

        // Update app settings state (without logo first)
        setAppSettings({
          logo: null, // Will be set by useEffect when logoFileList changes
          title: data.title || 'LibertyHolding xin chào!',
          subtitle: data.subtitle || 'Ngày mới tốt lành👋',
          primaryColor: data.primaryColor || '#2563eb',
          colorType: data.colorType || 'solid',
          gradientStart: data.gradientStart || '#667eea',
          gradientEnd: data.gradientEnd || '#764ba2',
        });

        // Update logo file list for SharedImageUpload (this will trigger useEffect to update logo)
        if (data.logo?.url) {
          setLogoFileList([
            {
              uid: data.logo.id || 'logo',
              name: data.logo.name || 'logo.png',
              status: 'done',
              url: data.logo.url,
            },
          ]);
        } else {
          setLogoFileList([]);
        }

        // Update form values
        form.setFieldsValue({
          title: data.title,
          subtitle: data.subtitle,
          colorType: data.colorType,
          primaryColor: data.primaryColor,
          gradientStart: data.gradientStart,
          gradientEnd: data.gradientEnd,
        });
      }
    } catch (error) {
      console.error('❌ Error reloading settings:', error);
    }
  };

  const onFinish = async (values) => {
    try {
      console.log('💾 Saving settings...');

      // Prepare data for API
      const settingsData = {
        title: appSettings.title,
        subtitle: appSettings.subtitle,
        colorType: appSettings.colorType,
        primaryColor: appSettings.primaryColor,
        gradientStart: appSettings.gradientStart,
        gradientEnd: appSettings.gradientEnd,
      };

      // Handle logo upload
      if (logoFileList.length > 0) {
        const logoFile = logoFileList[0];

        if (logoFile.originFileObj) {
          // New logo uploaded - upload to Strapi
          const formData = new FormData();
          formData.append('files', logoFile.originFileObj);

          try {
            const uploadResponse = await post('/upload', formData);
            if (
              uploadResponse &&
              uploadResponse.data &&
              uploadResponse.data[0]
            ) {
              settingsData.logo = uploadResponse.data[0].id;
            }
          } catch (uploadError) {
            console.error('❌ Error uploading logo:', uploadError);
            message.error('Không thể tải lên logo');
            return;
          }
        } else if (logoFile.url) {
          // Keep existing logo - find the ID from current settings
          const currentResponse = await get('/management/settings');
          if (currentResponse?.data?.data?.logo?.url === logoFile.url) {
            settingsData.logo = currentResponse.data.data.logo.id;
          }
        }
      }

      // Save settings using management plugin route
      const saveResponse = await put('/management/settings', settingsData);
      console.log('💾 Save response:', saveResponse);

      message.success('Cài đặt giao diện mini app đã được lưu thành công!');

      // Reload settings after successful save
      await reloadSettings();
    } catch (error) {
      console.error('❌ Error saving settings:', error);
      message.error('Có lỗi xảy ra khi lưu cài đặt. Vui lòng thử lại!');
    }
  };

  const handleSettingChange = (field, value) => {
    setAppSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Get primary color based on type
  const getPrimaryColor = () => {
    if (appSettings.colorType === 'gradient') {
      return `linear-gradient(135deg, ${appSettings.gradientStart} 0%, ${appSettings.gradientEnd} 100%)`;
    }
    return appSettings.primaryColor;
  };

  // Get primary color for text/icons (solid color only)
  const getPrimaryTextColor = () => {
    if (appSettings.colorType === 'gradient') {
      return appSettings.gradientStart; // Use start color for text
    }
    return appSettings.primaryColor;
  };

  // iPhone mockup component
  const PhoneMockup = () => (
    <div
      style={{
        width: 320,
        height: 640,
        background: '#000',
        borderRadius: 30,
        padding: 8,
        position: 'relative',
        boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
      }}
    >
      {/* iPhone notch */}
      <div
        style={{
          position: 'absolute',
          top: 10,
          left: '50%',
          transform: 'translateX(-50%)',
          width: 120,
          height: 25,
          background: '#000',
          borderRadius: 15,
          zIndex: 10,
        }}
      />

      {/* Screen */}
      <div
        style={{
          width: '100%',
          height: '100%',
          background: '#fff',
          borderRadius: 22,
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        {/* Status bar */}

        {/* Header */}
        <div
          style={{
            background: getPrimaryColor(),
            padding: '30px 8px 10px',
            display: 'flex',
            alignItems: 'center',
            gap: 8,
          }}
        >
          {appSettings.logo ? (
            <img
              src={appSettings.logo}
              alt="Logo"
              style={{
                width: 40,
                height: 40,
                borderRadius: 8,
                objectFit: 'cover',
              }}
            />
          ) : (
            <div
              style={{
                width: 40,
                height: 40,
                background: 'rgba(255,255,255,0.2)',
                borderRadius: 8,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
              }}
            >
              <AppstoreOutlined />
            </div>
          )}
          <div>
            <div
              style={{
                color: 'white',
                fontSize: 14,
                fontWeight: 600,
                fontFamily: 'Be Vietnam Pro',
                lineHeight: 1.2,
              }}
            >
              {appSettings.title}
            </div>
            <div
              style={{
                color: 'rgba(255,255,255,0.8)',
                fontSize: 12,
                fontFamily: 'Be Vietnam Pro',
              }}
            >
              {appSettings.subtitle}
            </div>
          </div>
        </div>

        {/* Banner Slider */}
        <div
          style={{
            height: 120,
            margin: '8px 8px',
            borderRadius: 5,
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Slides */}
          <div
            style={{
              display: 'flex',
              width: `${bannerSlides.length * 100}%`,
              height: '100%',
              transform: `translateX(-${
                (currentSlide * 100) / bannerSlides.length
              }%)`,
              transition: 'transform 0.5s ease-in-out',
            }}
          >
            {bannerSlides.map((slide, index) => (
              <div
                key={index}
                style={{
                  width: `${100 / bannerSlides.length}%`,
                  height: '100%',
                  background: slide.background,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontFamily: 'Be Vietnam Pro',
                  textAlign: 'center',
                  padding: '0 16px',
                }}
              >
                <div style={{ fontSize: 16, fontWeight: 600, marginBottom: 4 }}>
                  {slide.text}
                </div>
                <div style={{ fontSize: 12, opacity: 0.9 }}>
                  {slide.subtext}
                </div>
              </div>
            ))}
          </div>

          {/* Dots indicator */}
          <div
            style={{
              position: 'absolute',
              bottom: 8,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 6,
            }}
          >
            {bannerSlides.map((_, index) => (
              <div
                key={index}
                style={{
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  background:
                    currentSlide === index ? 'white' : 'rgba(255,255,255,0.5)',
                  transition: 'background 0.3s ease',
                }}
              />
            ))}
          </div>
        </div>

        {/* Categories */}
        <div style={{ padding: '0 8px', marginBottom: 16 }}>
          <div
            style={{
              fontSize: 16,
              fontWeight: 600,
              marginBottom: 12,
              fontFamily: 'Be Vietnam Pro',
              color: '#1a202c',
            }}
          >
            Danh mục
          </div>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(4, 1fr)',
              gap: 12,
            }}
          >
            {[
              { icon: <ShoppingOutlined />, name: 'Thời trang' },
              { icon: <MobileOutlined />, name: 'Điện tử' },
              { icon: <HomeOutlined />, name: 'Gia dụng' },
              { icon: <TagsOutlined />, name: 'Khuyến mãi' },
            ].map((category, index) => (
              <div
                key={index}
                style={{
                  textAlign: 'center',
                  padding: 8,
                }}
              >
                <div
                  style={{
                    width: 48,
                    height: 48,
                    background: `${getPrimaryTextColor()}15`,
                    borderRadius: 12,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 8px',
                    color: getPrimaryTextColor(),
                    fontSize: 20,
                  }}
                >
                  {category.icon}
                </div>
                <div
                  style={{
                    fontSize: 11,
                    color: '#4a5568',
                    fontFamily: 'Be Vietnam Pro',
                  }}
                >
                  {category.name}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Products */}
        <div style={{ padding: '0 8px', flex: 1 }}>
          <div
            style={{
              fontSize: 16,
              fontWeight: 600,
              marginBottom: 12,
              fontFamily: 'Be Vietnam Pro',
              color: '#1a202c',
            }}
          >
            Sản phẩm nổi bật
          </div>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: 12,
            }}
          >
            {[1, 2].map((item) => (
              <div
                key={item}
                style={{
                  background: '#f7fafc',
                  borderRadius: 8,
                  padding: 8,
                }}
              >
                <div
                  style={{
                    height: 80,
                    background: '#e2e8f0',
                    borderRadius: 6,
                    marginBottom: 8,
                  }}
                />
                <div
                  style={{
                    fontSize: 12,
                    fontWeight: 500,
                    marginBottom: 4,
                    fontFamily: 'Be Vietnam Pro',
                    color: '#2d3748',
                  }}
                >
                  Sản phẩm {item}
                </div>
                <div
                  style={{
                    fontSize: 12,
                    color: getPrimaryTextColor(),
                    fontWeight: 600,
                    fontFamily: 'Be Vietnam Pro',
                  }}
                >
                  299.000đ
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Navigation */}
        <div
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: 60,
            background: '#fff',
            borderTop: '1px solid #e2e8f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-around',
            paddingBottom: 8,
          }}
        >
          {[
            { icon: <HomeOutlined />, name: 'Trang chủ', active: true },
            { icon: <AppstoreOutlined />, name: 'Danh mục' },
            { icon: <ShoppingCartOutlined />, name: 'Giỏ hàng' },
            { icon: <HeartOutlined />, name: 'Yêu thích' },
            { icon: <UserOutlined />, name: 'Tài khoản' },
          ].map((nav, index) => (
            <div
              key={index}
              style={{
                textAlign: 'center',
                color: nav.active ? getPrimaryTextColor() : '#a0aec0',
              }}
            >
              <div style={{ fontSize: 18, marginBottom: 2 }}>{nav.icon}</div>
              <div style={{ fontSize: 10, fontFamily: 'Be Vietnam Pro' }}>
                {nav.name}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <PageHeader
        title="Cài đặt giao diện Mini App"
        description="Tùy chỉnh giao diện và trải nghiệm người dùng cho ứng dụng di động"
      />
      <CardContent>
        <div style={{ display: 'flex', gap: 32, alignItems: 'flex-start' }}>
          {/* Phone Preview */}
          <div style={{ flex: '0 0 auto' }}>
            <PhoneMockup />
          </div>

          {/* Settings Panel */}
          <div style={{ flex: 1 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
              className="settings-form"
              initialValues={appSettings}
            >
              <div
                style={{
                  borderBottom: '1px solid #e5e7eb',
                  paddingBottom: 16,
                  marginBottom: 24,
                }}
              >
                <Title
                  level={5}
                  style={{
                    marginBottom: 8,
                    fontFamily: 'Be Vietnam Pro',
                    color: '#374151',
                    fontSize: 16,
                    fontWeight: 600,
                  }}
                >
                  Thông tin ứng dụng
                </Title>
              </div>

              <Form.Item label="Logo ứng dụng" name="logo">
                <SharedImageUpload
                  value={logoFileList || []}
                  onChange={(files) => setLogoFileList(files || [])}
                  maxCount={1}
                  accept="image/*"
                  uploadText="Tải lên logo"
                />
              </Form.Item>

              <Form.Item label="Tiêu đề chính" name="title">
                <Input
                  placeholder="Nhập tiêu đề chính"
                  value={appSettings.title}
                  onChange={(e) => handleSettingChange('title', e.target.value)}
                />
              </Form.Item>

              <Form.Item label="Phụ đề" name="subtitle">
                <Input
                  placeholder="Nhập phụ đề"
                  value={appSettings.subtitle}
                  onChange={(e) =>
                    handleSettingChange('subtitle', e.target.value)
                  }
                />
              </Form.Item>

              <Form.Item label="Loại màu chủ đạo" name="colorType">
                <Select
                  value={appSettings.colorType}
                  onChange={(value) => handleSettingChange('colorType', value)}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="solid">Màu đơn</Select.Option>
                  <Select.Option value="gradient">Gradient</Select.Option>
                </Select>
              </Form.Item>

              {appSettings.colorType === 'solid' ? (
                <Form.Item label="Màu chủ đạo" name="primaryColor">
                  <Input
                    type="color"
                    value={appSettings.primaryColor}
                    onChange={(e) =>
                      handleSettingChange('primaryColor', e.target.value)
                    }
                    style={{ width: 100, height: 40 }}
                  />
                </Form.Item>
              ) : (
                <>
                  <Form.Item label="Màu bắt đầu" name="gradientStart">
                    <Input
                      type="color"
                      value={appSettings.gradientStart}
                      onChange={(e) =>
                        handleSettingChange('gradientStart', e.target.value)
                      }
                      style={{ width: 100, height: 40 }}
                    />
                  </Form.Item>
                  <Form.Item label="Màu kết thúc" name="gradientEnd">
                    <Input
                      type="color"
                      value={appSettings.gradientEnd}
                      onChange={(e) =>
                        handleSettingChange('gradientEnd', e.target.value)
                      }
                      style={{ width: 100, height: 40 }}
                    />
                  </Form.Item>
                </>
              )}

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  className="settings-save-btn"
                  icon={<SaveOutlined />}
                >
                  Lưu cài đặt
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const Settings = () => {
  const history = useHistory();
  const location = useLocation();

  const menuItems = [
    {
      key: 'general',
      icon: <SettingOutlined />,
      label: 'Cài đặt chung',
      path: '/plugins/management/settings/general',
    },
    {
      key: 'company',
      icon: <BuildOutlined />,
      label: 'Cài đặt công ty',
      path: '/plugins/management/settings/company',
    },

    {
      key: 'miniapp',
      icon: <MobileOutlined />,
      label: 'Giao diện Mini App',
      path: '/plugins/management/settings/miniapp',
    },
    {
      key: 'payment',
      icon: <CreditCardOutlined />,
      label: 'Phương thức thanh toán',
      path: '/plugins/management/settings/payment',
    },
  ];

  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.includes('/settings/general')) return 'general';
    if (path.includes('/settings/company')) return 'company';
    if (path.includes('/settings/miniapp')) return 'miniapp';
    if (path.includes('/settings/payment')) return 'payment';
    return 'general';
  };

  const handleMenuClick = (item) => {
    history.push(item.path);
  };

  return (
    <PageContainer>
      <SettingsContainer>
        <Layout className="settings-layout">
          <Sider width={280} className="settings-sider">
            <div style={{ padding: '20px 0' }}>
              <Title
                level={4}
                style={{
                  textAlign: 'center',
                  margin: '0 0 20px 0',
                  fontFamily: 'Be Vietnam Pro',
                  color: '#1a202c',
                }}
              >
                Cài đặt hệ thống
              </Title>
              <Menu
                mode="inline"
                selectedKeys={[getSelectedKey()]}
                className="settings-menu"
                items={menuItems.map((item) => ({
                  key: item.key,
                  icon: item.icon,
                  label: item.label,
                  onClick: () => handleMenuClick(item),
                }))}
              />
            </div>
          </Sider>

          <Layout>
            <Content className="settings-content">
              <Switch>
                <Route
                  path="/plugins/management/settings/general"
                  component={GeneralSettings}
                  exact
                />
                <Route
                  path="/plugins/management/settings/company"
                  component={CompanySettings}
                  exact
                />
                <Route
                  path="/plugins/management/settings/miniapp"
                  component={MiniAppSettings}
                  exact
                />
                <Route
                  path="/plugins/management/settings/payment"
                  component={PaymentSettings}
                  exact
                />
                <Route
                  path="/plugins/management/settings"
                  component={GeneralSettings}
                  exact
                />
              </Switch>
            </Content>
          </Layout>
        </Layout>
      </SettingsContainer>
    </PageContainer>
  );
};

export default Settings;
